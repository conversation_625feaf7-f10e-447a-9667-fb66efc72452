# 数据获取和使用指南

## 📁 数据文件说明

### 1. 气候数据文件

#### meanEAR5.xlsx (真实数据)
- **来源**: ERA5重分析数据
- **获取方法**: 
  1. 注册 https://cds.climate.copernicus.eu/
  2. 运行 `python era5_data_guide.py` 查看详细获取步骤
  3. 使用提供的脚本下载和处理数据

#### meanEAR5_sample.xlsx (示例数据)
- **来源**: 自动生成的示例数据
- **用途**: 学习和测试
- **生成方法**: 运行 `python era5_data_guide.py`

**数据格式**:
```
lat,lon,month,t2m,rh
34.0,107.0,1,15.2,0.65
34.0,107.0,2,18.5,0.70
...
```

### 2. 同位素数据文件

#### C-Iso-GuanZhong2.csv
- **来源**: 降水同位素观测数据
- **内容**: 各站点12个月的δ²H和δ¹⁸O数据
- **获取**: 已自动创建示例数据

**数据格式**:
```
lat,lon,ele,hyd1,oxy1,hyd2,oxy2,...,hyd12,oxy12
34.1,107.2,420.5,-42.3,-6.2,-36.8,-5.8,...,-44.2,-6.4
```

### 3. 计算点位文件

#### EL_slope_points.csv
- **内容**: 需要计算蒸发线斜率的点位坐标
- **生成**: 自动创建或手动指定

**数据格式**:
```
id,lon,lat,name
0,108.07,34.62,Point_1
1,108.60,34.34,Point_2
```

## 🚀 运行方法

### 方法1: 使用独立版本 (推荐)
```bash
python data_processing_standalone.py
```

**特点**:
- ✅ 不需要QGIS环境
- ✅ 自动处理缺失文件
- ✅ 完整的错误处理
- ✅ 详细的运行日志

### 方法2: 使用原始版本 (需要QGIS)
```bash
python data_processing.py
```

**要求**:
- 需要QGIS环境
- 需要加载相应的图层
- 需要手动准备所有数据文件

## 📊 输出结果

### 1. 插值数据文件
- **文件名**: `EL_interpolated_data_standalone.csv`
- **内容**: 每个目标点位12个月的气候和同位素数据
- **用途**: 中间结果，用于斜率计算

### 2. 斜率结果文件
- **文件名**: `EL_slope_results_standalone.csv`
- **内容**: 每个点位每个月不同n值下的蒸发线斜率
- **用途**: 最终分析结果

## 🔧 自定义使用

### 1. 修改计算点位
编辑 `EL_slope_points.csv` 文件，添加你的目标点位:
```csv
id,lon,lat,name
0,108.5,34.5,我的点位1
1,109.0,34.8,我的点位2
```

### 2. 使用真实数据
1. 按照 `era5_data_guide.py` 的指导获取ERA5数据
2. 准备真实的同位素观测数据
3. 替换示例数据文件

### 3. 调整计算参数
在脚本中修改以下参数:
- `n` 值: 水体类型 (0.5=开放水体, 1.0=土壤水)
- 插值点数: 默认使用最近的4个点
- 权重方法: 默认使用反距离权重

## 📈 结果解读

### 蒸发线斜率含义
- **Gibson方法**: 基于Gibson et al. (2008)的计算方法
- **Benettin方法**: 基于Benettin et al. (2018)的计算方法
- **数值范围**: 通常在2-5之间
- **影响因素**: 温度、湿度、水体类型

### 典型斜率值
| 水体类型 | n值 | 典型斜率范围 |
|---------|-----|-------------|
| 开放水体 | 0.5 | 3.5-4.0 |
| 部分覆盖 | 0.75 | 3.0-3.5 |
| 土壤水 | 1.0 | 2.5-3.0 |

## ⚠️ 注意事项

### 1. 数据质量
- 示例数据仅用于学习和测试
- 实际研究请使用真实观测数据
- 注意数据的时空匹配性

### 2. 计算精度
- 插值方法会引入误差
- 建议使用多种方法验证结果
- 考虑地形和气候的复杂性

### 3. 适用范围
- 方法适用于温带和干旱区
- 不同气候区可能需要调整参数
- 注意季节性变化的影响

## 🆘 常见问题

### Q1: 找不到数据文件
**解决方法**:
1. 运行 `python era5_data_guide.py` 创建示例数据
2. 检查文件路径是否正确
3. 确保文件格式正确

### Q2: 计算结果异常
**可能原因**:
1. 输入数据超出合理范围
2. 插值点距离过远
3. 同位素数据缺失

### Q3: 运行速度慢
**优化方法**:
1. 减少计算点位数量
2. 降低插值精度
3. 使用更快的计算机

## 📞 技术支持

如果遇到问题，请检查:
1. Python环境和依赖包
2. 数据文件格式和内容
3. 计算参数设置

**联系方式**:
- Email: <EMAIL>
- Email: <EMAIL>

## 📚 相关文献

1. Ren X., Li P., He X., Zhang Q., 2024. Tracing the sources and evaporation fate of surface water and groundwater using stable isotopes of hydrogen and oxygen. Science of the Total Environment, 172708.

2. Gibson, J.J., Birks, S.J., Edwards, T.W.D., 2008. Global prediction of δA and δ2H-δ18O evaporation slopes for lakes and soil water accounting for seasonality. Global Biogeochemical Cycles 22.

3. Benettin, P., Volkmann, T.H.M., von Freyberg, J., et al., 2018. Effects of climatic seasonality on the isotopic composition of evaporating soil waters. Hydrology and Earth System Sciences 22, 2881–2890.
