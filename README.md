# Evaporation-lines-slope

require： QGIS, python

require python packages: pandas, numpy

1. file: data_processing.py

Preparing data for calculating the evaporation line slope

2. file：EL_slope.py

Calculating the slope

## Details   
The code is used to calculate the evaporation line slope in the paper: **<PERSON>, Li <PERSON>, He <PERSON>, Zhang Q., 2024. Tracing the sources and evaporation fate of surface water and groundwater using stable isotopes of hydrogen and oxygen.** _**Science of the Total Environment**_**, 172708. [https://doi.org/10.1016/j.scitotenv.2024.172708](https://doi.org/10.1016/j.scitotenv.2024.172708)**

**If you have any queries, please contact via email.**
1. <EMAIL>
2. <EMAIL>
