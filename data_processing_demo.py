# -*- coding: utf-8 -*-
# 数据处理演示脚本 - 模拟版本
# 原始脚本需要在QGIS环境中运行，这里提供功能演示

import pandas as pd
import numpy as np
import time
from EL_slope import slope_line

def create_sample_data():
    """创建示例数据，模拟真实的气候和同位素数据"""
    print("创建示例数据...")
    
    # 模拟气候数据 (ERA5重分析数据)
    np.random.seed(42)  # 确保结果可重现
    
    # 创建网格点
    lats = np.arange(34.0, 35.0, 0.1)  # 纬度范围
    lons = np.arange(108.0, 109.0, 0.1)  # 经度范围
    
    climate_data = []
    for lat in lats:
        for lon in lons:
            for month in range(1, 13):
                # 模拟温度数据 (考虑季节变化)
                base_temp = 15 + 10 * np.sin((month - 1) * np.pi / 6)  # 季节变化
                temp = base_temp + np.random.normal(0, 2)  # 添加随机噪声
                
                # 模拟相对湿度数据
                base_rh = 0.65 + 0.15 * np.sin((month - 1) * np.pi / 6 + np.pi/4)
                rh = max(0.3, min(0.9, base_rh + np.random.normal(0, 0.05)))
                
                climate_data.append([lat, lon, month, temp, rh])
    
    climate_df = pd.DataFrame(climate_data, columns=['lat', 'lon', 'month', 't2m', 'rh'])
    
    # 模拟同位素数据
    iso_data = []
    # 选择一些代表性点位
    iso_lats = np.arange(34.1, 34.9, 0.2)
    iso_lons = np.arange(108.1, 108.9, 0.2)
    
    for lat in iso_lats:
        for lon in iso_lons:
            elevation = 400 + np.random.normal(0, 100)  # 模拟海拔
            
            for month in range(1, 13):
                # 模拟降水同位素组成 (考虑季节和海拔效应)
                base_d2h = -40 + 5 * np.sin((month - 1) * np.pi / 6)  # 季节变化
                base_d18o = -6 + 0.8 * np.sin((month - 1) * np.pi / 6)
                
                # 海拔效应
                d2h = base_d2h - (elevation - 400) * 0.02 + np.random.normal(0, 3)
                d18o = base_d18o - (elevation - 400) * 0.003 + np.random.normal(0, 0.5)
                
                iso_data.append([lat, lon, elevation, month, d2h, d18o])
    
    iso_df = pd.DataFrame(iso_data, columns=['lat', 'lon', 'ele', 'month', f'hyd', f'oxy'])
    
    # 重新整理同位素数据格式以匹配原始代码
    iso_pivot = []
    for (lat, lon, ele), group in iso_df.groupby(['lat', 'lon', 'ele']):
        row = [lat, lon, ele]
        for month in range(1, 13):
            month_data = group[group['month'] == month]
            if not month_data.empty:
                row.extend([month_data['hyd'].iloc[0], month_data['oxy'].iloc[0]])
            else:
                row.extend([-40, -6])  # 默认值
        iso_pivot.append(row)
    
    # 创建列名
    iso_columns = ['lat', 'lon', 'ele']
    for month in range(1, 13):
        iso_columns.extend([f'hyd{month}', f'oxy{month}'])
    
    iso_df_final = pd.DataFrame(iso_pivot, columns=iso_columns)
    
    return climate_df, iso_df_final

def calculate_distance(point1, point2):
    """计算两点间的距离 (简化版本，使用欧几里得距离)"""
    lat1, lon1 = point1
    lat2, lon2 = point2
    
    # 简化的距离计算 (实际应该使用大圆距离)
    dlat = (lat2 - lat1) * 111000  # 纬度1度约111km
    dlon = (lon2 - lon1) * 111000 * np.cos(np.radians((lat1 + lat2) / 2))
    
    return np.sqrt(dlat**2 + dlon**2)

def find_nearest_points(target_point, data_points, n=4):
    """找到目标点附近的n个最近点"""
    distances = []
    
    for idx, row in data_points.iterrows():
        point = (row['lat'], row['lon'])
        dist = calculate_distance(target_point, point)
        distances.append((idx, dist))
    
    # 按距离排序，取前n个
    distances.sort(key=lambda x: x[1])
    nearest_indices = [idx for idx, dist in distances[:n]]
    nearest_distances = [dist for idx, dist in distances[:n]]
    
    return nearest_indices, nearest_distances

def inverse_distance_weighting(values, distances, power=1):
    """反距离权重插值"""
    if min(distances) == 0:
        # 如果有点距离为0，直接返回该点的值
        zero_idx = distances.index(0)
        return values[zero_idx]
    
    weights = [1 / (d ** power) for d in distances]
    weight_sum = sum(weights)
    
    weighted_value = sum(v * w for v, w in zip(values, weights)) / weight_sum
    return weighted_value

def process_target_points():
    """处理目标点的数据插值"""
    print("开始数据处理演示...")
    
    # 创建示例数据
    climate_df, iso_df = create_sample_data()
    
    print(f"气候数据点数: {len(climate_df)}")
    print(f"同位素数据点数: {len(iso_df)}")
    print()
    
    # 定义一些目标点 (模拟需要计算蒸发线斜率的位置)
    target_points = [
        (34.2, 108.3, "点位1"),
        (34.5, 108.6, "点位2"), 
        (34.7, 108.8, "点位3"),
    ]
    
    results = []
    
    for target_lat, target_lon, point_name in target_points:
        print(f"处理 {point_name} (纬度: {target_lat}, 经度: {target_lon})")
        
        # 获取每个月的数据
        for month in range(1, 13):
            # 获取该月的气候数据
            month_climate = climate_df[climate_df['month'] == month]
            
            # 找到最近的气候站点
            unique_climate_points = month_climate[['lat', 'lon']].drop_duplicates().reset_index(drop=True)
            climate_indices, climate_distances = find_nearest_points(
                (target_lat, target_lon),
                unique_climate_points
            )

            # 插值温度和湿度 - 需要从原始数据中获取对应的值
            temps = []
            rhs = []
            for idx in climate_indices:
                point_lat = unique_climate_points.iloc[idx]['lat']
                point_lon = unique_climate_points.iloc[idx]['lon']
                point_data = month_climate[
                    (month_climate['lat'] == point_lat) &
                    (month_climate['lon'] == point_lon)
                ].iloc[0]
                temps.append(point_data['t2m'])
                rhs.append(point_data['rh'])
            
            interp_temp = inverse_distance_weighting(temps, climate_distances)
            interp_rh = inverse_distance_weighting(rhs, climate_distances)
            
            # 找到最近的同位素站点并插值
            iso_points = iso_df[['lat', 'lon']].reset_index(drop=True)
            iso_indices, iso_distances = find_nearest_points(
                (target_lat, target_lon),
                iso_points
            )

            # 获取该月的同位素数据
            hyd_col = f'hyd{month}'
            oxy_col = f'oxy{month}'

            hyds = []
            oxys = []
            for idx in iso_indices:
                point_lat = iso_points.iloc[idx]['lat']
                point_lon = iso_points.iloc[idx]['lon']
                point_data = iso_df[
                    (iso_df['lat'] == point_lat) &
                    (iso_df['lon'] == point_lon)
                ].iloc[0]
                hyds.append(point_data[hyd_col])
                oxys.append(point_data[oxy_col])
            
            interp_hyd = inverse_distance_weighting(hyds, iso_distances)
            interp_oxy = inverse_distance_weighting(oxys, iso_distances)
            
            # 保存结果
            results.append([
                target_lon, target_lat, month, interp_temp, interp_rh, 
                interp_hyd, interp_oxy, point_name
            ])
            
            print(f"  月份 {month:2d}: T={interp_temp:5.1f}°C, RH={interp_rh:.2f}, "
                  f"δ2H={interp_hyd:6.1f}‰, δ18O={interp_oxy:5.1f}‰")
        
        print()
    
    # 创建结果DataFrame
    result_df = pd.DataFrame(results, columns=[
        'lon', 'lat', 'month', 't2m', 'rh', '2H', '18O', 'point_name'
    ])
    
    return result_df

def calculate_slopes_for_points(data_df):
    """为每个点计算蒸发线斜率"""
    print("计算蒸发线斜率...")
    
    slope_results = []
    
    for point_name in data_df['point_name'].unique():
        point_data = data_df[data_df['point_name'] == point_name]
        
        print(f"\n{point_name} 的蒸发线斜率计算:")
        print("月份\t温度\t湿度\tGibson斜率\tBenettin斜率")
        print("-" * 50)
        
        for month in range(1, 13):
            month_data = point_data[point_data['month'] == month].iloc[0]
            
            precipitation = [month_data['2H'], month_data['18O']]
            temperature = month_data['t2m']
            humidity = month_data['rh']
            
            # 计算不同条件下的斜率
            n_values = [0.5, 0.75, 1.0]  # 开放水体、中间状态、土壤水
            
            for n in n_values:
                slope_gibson = slope_line(precipitation, humidity, temperature, n, 0)
                slope_benettin = slope_line(precipitation, humidity, temperature, n, 1)
                
                slope_results.append([
                    point_name, month, temperature, humidity, 
                    precipitation[0], precipitation[1], n,
                    slope_gibson, slope_benettin
                ])
            
            # 显示n=0.75的结果
            slope_g = slope_line(precipitation, humidity, temperature, 0.75, 0)
            slope_b = slope_line(precipitation, humidity, temperature, 0.75, 1)
            
            print(f"{month:2d}\t{temperature:5.1f}\t{humidity:.2f}\t{slope_g:.3f}\t\t{slope_b:.3f}")
    
    slope_df = pd.DataFrame(slope_results, columns=[
        'point_name', 'month', 'temperature', 'humidity', 
        'delta_2H', 'delta_18O', 'n_value',
        'slope_gibson', 'slope_benettin'
    ])
    
    return slope_df

def main():
    """主函数"""
    print("=" * 60)
    print("数据处理演示程序")
    print("=" * 60)
    print()
    
    print("注意: 原始 data_processing.py 需要在 QGIS 环境中运行，")
    print("并需要特定的数据文件。这里提供功能演示版本。")
    print()
    
    start_time = time.time()
    
    try:
        # 处理目标点数据
        interpolated_data = process_target_points()
        
        # 计算蒸发线斜率
        slope_results = calculate_slopes_for_points(interpolated_data)
        
        # 保存结果
        interpolated_data.to_csv('interpolated_data_demo.csv', index=False)
        slope_results.to_csv('slope_results_demo.csv', index=False)
        
        print(f"\n处理完成! 用时: {time.time() - start_time:.2f} 秒")
        print("\n生成的文件:")
        print("- interpolated_data_demo.csv: 插值后的气候和同位素数据")
        print("- slope_results_demo.csv: 蒸发线斜率计算结果")
        
        # 显示统计信息
        print(f"\n统计信息:")
        print(f"处理的点位数: {len(interpolated_data['point_name'].unique())}")
        print(f"总数据记录数: {len(interpolated_data)}")
        print(f"斜率计算结果数: {len(slope_results)}")
        
        print("\n平均斜率 (n=0.75):")
        avg_data = slope_results[slope_results['n_value'] == 0.75]
        avg_gibson = avg_data['slope_gibson'].mean()
        avg_benettin = avg_data['slope_benettin'].mean()
        print(f"Gibson方法: {avg_gibson:.3f}")
        print(f"Benettin方法: {avg_benettin:.3f}")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
