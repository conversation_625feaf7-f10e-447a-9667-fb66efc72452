# -*- coding: utf-8 -*-
# <AUTHOR> <PERSON><PERSON><PERSON> (修改版本，不依赖QGIS)
# @FileName  : data_processing_standalone.py

# 独立运行版本，不需要QGIS环境
# 获取气候数据和同位素数据(反距离权重法)用于计算空间点位的斜率

import pandas as pd
import numpy as np
import time
from EL_slope import slope_line

T1 = time.time()

def calculate_distance(point1, point2):
    """
    计算两点间距离 (简化版本，使用球面距离公式)
    
    point1, point2: [longitude, latitude]
    return: 距离 (米)
    """
    lon1, lat1 = point1[0], point1[1]
    lon2, lat2 = point2[0], point2[1]
    
    # 转换为弧度
    lat1_rad = np.radians(lat1)
    lat2_rad = np.radians(lat2)
    dlat = np.radians(lat2 - lat1)
    dlon = np.radians(lon2 - lon1)
    
    # Haversine公式
    a = np.sin(dlat/2)**2 + np.cos(lat1_rad) * np.cos(lat2_rad) * np.sin(dlon/2)**2
    c = 2 * np.arcsin(np.sqrt(a))
    
    # 地球半径 (米)
    R = 6371000
    
    return R * c

def find_nearest_points(target_point, data_points, n=4):
    """
    找到最近的n个点
    
    target_point: [longitude, latitude]
    data_points: DataFrame with 'lat', 'lon' columns
    n: 返回最近的n个点
    
    return: (indices, distances, weights)
    """
    distances = []
    
    for idx, row in data_points.iterrows():
        point = [row['lon'], row['lat']]
        dist = calculate_distance(target_point, point)
        distances.append((idx, dist))
    
    # 按距离排序，取最近的n个点
    distances.sort(key=lambda x: x[1])
    nearest = distances[:n]
    
    indices = [idx for idx, dist in nearest]
    dists = [dist for idx, dist in nearest]
    
    # 计算权重 (反距离权重)
    # 避免除零错误
    min_dist = 1.0  # 最小距离设为1米
    weights = [1 / max(dist, min_dist) for dist in dists]
    weight_sum = sum(weights)
    weights = [w / weight_sum for w in weights]  # 归一化权重
    
    return indices, dists, weights

def match_climate_values(target_point, climate_data):
    """
    为目标点匹配气候数据
    
    target_point: [longitude, latitude]
    climate_data: 气候数据DataFrame
    
    return: 插值后的气候数据字典
    """
    # 获取唯一的气候站点
    unique_points = climate_data[['lat', 'lon']].drop_duplicates().reset_index(drop=True)
    
    # 找到最近的4个气候站点
    indices, distances, weights = find_nearest_points(target_point, unique_points)
    
    # 为每个月计算插值结果
    result = {}
    
    for month in range(1, 13):
        month_data = climate_data[climate_data['month'] == month]
        
        temp_values = []
        rh_values = []
        
        for idx in indices:
            point_lat = unique_points.iloc[idx]['lat']
            point_lon = unique_points.iloc[idx]['lon']
            
            # 找到对应的气候数据
            point_climate = month_data[
                (month_data['lat'] == point_lat) & 
                (month_data['lon'] == point_lon)
            ]
            
            if len(point_climate) > 0:
                temp_values.append(point_climate.iloc[0]['t2m'])
                rh_values.append(point_climate.iloc[0]['rh'])
            else:
                # 如果没有找到数据，使用平均值
                temp_values.append(month_data['t2m'].mean())
                rh_values.append(month_data['rh'].mean())
        
        # 加权平均
        weighted_temp = sum(t * w for t, w in zip(temp_values, weights))
        weighted_rh = sum(r * w for r, w in zip(rh_values, weights))
        
        result[f't2m_{month}'] = weighted_temp
        result[f'rh_{month}'] = weighted_rh
    
    return result

def match_isotope_values(target_point, isotope_data):
    """
    为目标点匹配同位素数据
    
    target_point: [longitude, latitude]
    isotope_data: 同位素数据DataFrame
    
    return: 插值后的同位素数据字典
    """
    # 找到最近的4个同位素站点
    indices, distances, weights = find_nearest_points(target_point, isotope_data)
    
    result = {}
    
    for month in range(1, 13):
        hyd_col = f'hyd{month}'
        oxy_col = f'oxy{month}'
        
        hyd_values = []
        oxy_values = []
        
        for idx in indices:
            hyd_values.append(isotope_data.iloc[idx][hyd_col])
            oxy_values.append(isotope_data.iloc[idx][oxy_col])
        
        # 加权平均
        weighted_hyd = sum(h * w for h, w in zip(hyd_values, weights))
        weighted_oxy = sum(o * w for o, w in zip(oxy_values, weights))
        
        result[f'hyd{month}'] = weighted_hyd
        result[f'oxy{month}'] = weighted_oxy
    
    return result

def get_interpolated_data(target_point, climate_data, isotope_data):
    """
    获取目标点的插值数据
    
    target_point: [longitude, latitude]
    climate_data: 气候数据DataFrame
    isotope_data: 同位素数据DataFrame
    
    return: 插值后的数据DataFrame
    """
    # 获取气候数据
    climate_values = match_climate_values(target_point, climate_data)
    
    # 获取同位素数据
    isotope_values = match_isotope_values(target_point, isotope_data)
    
    # 组合数据
    data = []
    for month in range(1, 13):
        row = [
            target_point[0],  # longitude
            target_point[1],  # latitude
            month,
            climate_values[f't2m_{month}'],
            climate_values[f'rh_{month}'],
            isotope_values[f'hyd{month}'],
            isotope_values[f'oxy{month}']
        ]
        data.append(row)
    
    return pd.DataFrame(data, columns=['lon', 'lat', 'month', 't2m', 'rh', '2H', '18O'])

def create_sample_slope_points():
    """创建示例斜率计算点位"""
    print("创建示例斜率计算点位...")
    
    # 创建一些需要计算斜率的点位
    slope_points = []
    
    # 在研究区域内创建一些点位
    np.random.seed(123)
    for i in range(5):  # 创建5个点位进行测试
        lat = np.random.uniform(34.2, 34.8)
        lon = np.random.uniform(107.5, 109.5)
        slope_points.append([i, lon, lat, f'Point_{i+1}'])
    
    slope_df = pd.DataFrame(slope_points, columns=['id', 'lon', 'lat', 'name'])
    slope_df.to_csv('EL_slope_points.csv', index=False)
    
    print(f"斜率计算点位已创建: EL_slope_points.csv")
    print(f"点位数: {len(slope_df)}")
    
    return slope_df

def calculate_evaporation_slopes(interpolated_data):
    """
    计算蒸发线斜率
    
    interpolated_data: 插值后的数据DataFrame
    
    return: 斜率计算结果DataFrame
    """
    print("计算蒸发线斜率...")
    
    slope_results = []
    
    # 按点位分组
    for (lon, lat), group in interpolated_data.groupby(['lon', 'lat']):
        point_name = f"Point_({lat:.2f},{lon:.2f})"
        
        print(f"  计算点位 {point_name} 的斜率...")
        
        for month in range(1, 13):
            month_data = group[group['month'] == month].iloc[0]
            
            precipitation = [month_data['2H'], month_data['18O']]
            temperature = month_data['t2m']
            humidity = month_data['rh']
            
            # 计算不同n值下的斜率
            for n in [0.5, 0.75, 1.0]:
                try:
                    slope_gibson = slope_line(precipitation, humidity, temperature, n, 0)
                    slope_benettin = slope_line(precipitation, humidity, temperature, n, 1)
                    
                    slope_results.append([
                        point_name, lon, lat, month, temperature, humidity,
                        precipitation[0], precipitation[1], n,
                        slope_gibson, slope_benettin
                    ])
                except Exception as e:
                    print(f"    警告: 计算斜率时出错 (月份{month}, n={n}): {e}")
                    slope_results.append([
                        point_name, lon, lat, month, temperature, humidity,
                        precipitation[0], precipitation[1], n,
                        np.nan, np.nan
                    ])
    
    slope_df = pd.DataFrame(slope_results, columns=[
        'point_name', 'lon', 'lat', 'month', 'temperature', 'humidity',
        'delta_2H', 'delta_18O', 'n_value', 'slope_gibson', 'slope_benettin'
    ])
    
    return slope_df

def main():
    """主函数"""
    print("=" * 80)
    print("独立版本数据处理脚本")
    print("=" * 80)
    print()
    
    try:
        # 1. 读取气候数据
        print("1. 读取气候数据...")
        try:
            climate_data = pd.read_excel('./meanEAR5.xlsx')
            print("   使用真实的meanEAR5.xlsx文件")
        except FileNotFoundError:
            try:
                climate_data = pd.read_excel('./meanEAR5_sample.xlsx')
                print("   使用示例的meanEAR5_sample.xlsx文件")
            except FileNotFoundError:
                print("   错误: 找不到气候数据文件")
                print("   请运行 era5_data_guide.py 创建示例数据")
                return
        
        print(f"   气候数据点数: {len(climate_data)}")
        
        # 2. 读取同位素数据
        print("\n2. 读取同位素数据...")
        try:
            isotope_data = pd.read_csv('./C-Iso-GuanZhong2.csv')
            print("   成功读取同位素数据文件")
        except FileNotFoundError:
            print("   错误: 找不到同位素数据文件 C-Iso-GuanZhong2.csv")
            print("   文件应该已经创建，请检查文件是否存在")
            return
        
        print(f"   同位素数据点数: {len(isotope_data)}")
        
        # 3. 创建或读取斜率计算点位
        print("\n3. 准备斜率计算点位...")
        try:
            slope_points = pd.read_csv('./EL_slope_points.csv')
            print("   使用现有的斜率计算点位文件")
        except FileNotFoundError:
            slope_points = create_sample_slope_points()
        
        # 4. 处理每个目标点
        print("\n4. 开始数据插值...")
        all_interpolated_data = []
        
        for idx, row in slope_points.iterrows():
            target_point = [row['lon'], row['lat']]
            point_name = row['name']
            
            print(f"   处理 {point_name} (经度: {row['lon']:.2f}, 纬度: {row['lat']:.2f})")
            
            point_data = get_interpolated_data(target_point, climate_data, isotope_data)
            all_interpolated_data.append(point_data)
        
        # 5. 合并所有插值数据
        interpolated_df = pd.concat(all_interpolated_data, ignore_index=True)
        
        # 6. 计算蒸发线斜率
        print("\n5. 计算蒸发线斜率...")
        slope_results = calculate_evaporation_slopes(interpolated_df)
        
        # 7. 保存结果
        print("\n6. 保存结果...")
        interpolated_df.to_csv('EL_interpolated_data_standalone.csv', index=False)
        slope_results.to_csv('EL_slope_results_standalone.csv', index=False)
        
        # 8. 显示统计信息
        processing_time = time.time() - T1
        
        print(f"\n处理完成! 用时: {processing_time:.2f} 秒")
        print(f"\n生成的文件:")
        print(f"- EL_interpolated_data_standalone.csv: 插值后的数据 ({len(interpolated_df)} 条记录)")
        print(f"- EL_slope_results_standalone.csv: 斜率计算结果 ({len(slope_results)} 条记录)")
        
        # 显示一些统计信息
        print(f"\n统计信息:")
        print(f"处理的点位数: {len(slope_points)}")
        print(f"平均温度: {interpolated_df['t2m'].mean():.1f}°C")
        print(f"平均湿度: {interpolated_df['rh'].mean():.2f}")
        print(f"平均δ2H: {interpolated_df['2H'].mean():.1f}‰")
        print(f"平均δ18O: {interpolated_df['18O'].mean():.1f}‰")
        
        # 显示平均斜率
        if not slope_results.empty:
            avg_slopes = slope_results.groupby('n_value')[['slope_gibson', 'slope_benettin']].mean()
            print(f"\n平均蒸发线斜率:")
            for n_val in [0.5, 0.75, 1.0]:
                if n_val in avg_slopes.index:
                    water_type = "开放水体" if n_val == 0.5 else "土壤水" if n_val == 1.0 else "部分覆盖"
                    gibson_avg = avg_slopes.loc[n_val, 'slope_gibson']
                    benettin_avg = avg_slopes.loc[n_val, 'slope_benettin']
                    print(f"n={n_val} ({water_type}): Gibson={gibson_avg:.3f}, Benettin={benettin_avg:.3f}")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
