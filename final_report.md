# 蒸发线斜率计算项目 - 完整运行报告

## 📋 项目概述

本项目基于 **<PERSON>. et al. (2024)** 在 *Science of the Total Environment* 发表的研究，用于计算蒸发线斜率，追踪地表水和地下水的来源及蒸发过程。

**论文引用**: <PERSON>, <PERSON>, He <PERSON>, Zhang Q<PERSON>, 2024. Tracing the sources and evaporation fate of surface water and groundwater using stable isotopes of hydrogen and oxygen. Science of the Total Environment, 172708.

## 🔬 科学原理

利用氢氧稳定同位素(δ²H, δ¹⁸O)分析水体蒸发过程：
- **平衡分馏**: 液-气相变过程中的同位素分馏
- **动力学分馏**: 蒸发过程中的扩散效应
- **蒸发线斜率**: 定量化蒸发强度和环境条件影响

## 📁 项目文件结构

### 核心模块
- `EL_slope.py` - 核心计算模块，包含所有蒸发线斜率计算函数
- `data_processing.py` - 原始QGIS数据预处理模块
- `README.md` - 项目说明文档

### 演示和测试脚本
- `test_EL_slope.py` - 基础功能测试
- `analysis_EL_slope.py` - 参数敏感性分析
- `data_processing_demo.py` - 数据处理演示
- `data_processing_with_sample.py` - 使用示例数据的完整数据处理
- `demo_complete.py` - 完整功能演示
- `project_summary.py` - 项目总结
- `era5_data_guide.py` - ERA5数据获取指南

### 数据文件
- `meanEAR5_sample.xlsx` - 示例气候数据 (4,464条记录)
- `C-Iso-GuanZhong2_sample.csv` - 示例同位素数据 (45个站点)
- `EL_slope_points_sample.csv` - 示例计算点位 (10个点位)

### 结果文件
- `EL_interpolated_data.csv` - 插值后的气候和同位素数据 (120条记录)
- `EL_slope_results.csv` - 蒸发线斜率计算结果 (360条记录)
- `humidity_effect.png` - 湿度影响分析图
- `temperature_effect.png` - 温度影响分析图
- `n_effect.png` - n值影响分析图

## 🧮 核心计算函数

### 1. 温度转换
```python
Tk(t) # 摄氏度转开尔文
```

### 2. 平衡分馏系数 (α⁺)
```python
alpha_plus(atom, T) # atom: 'H'或'O', T: 开尔文温度
```
基于 Horita and Wesolowski (1994) 公式

### 3. 平衡分馏因子 (ε⁺)
```python
epsilon_plus(alpha) # ε⁺ = (α⁺-1) × 1000
```

### 4. 动力学分馏因子 (εₖ)
```python
epsilon_k(atom, n, method, h)
```
- **method 0**: Gibson et al. (2008)
- **method 1**: Benettin et al. (2018)

### 5. 大气水汽同位素组成 (δₐ)
```python
delta_A(delta_P, epsilon, alpha) # δₐ = (δₚ-ε⁺)/α⁺
```

### 6. 蒸发水同位素组成 (dX)
```python
dX(atom, Pre, h, t, n, method)
```

### 7. 蒸发线斜率
```python
slope_line(Pre, h, t, n, method) # 斜率 = dX_H / dX_O
```

## 📊 运行结果统计

### 数据处理统计
- **处理点位数**: 10个
- **平均温度**: 15.0°C
- **平均湿度**: 0.65
- **平均δ²H**: -39.0‰
- **平均δ¹⁸O**: -5.9‰

### 平均蒸发线斜率
| 水体类型 | n值 | Gibson方法 | Benettin方法 |
|---------|-----|-----------|-------------|
| 开放水体 | 0.5 | 3.785 | 3.845 |
| 部分覆盖 | 0.75 | 3.124 | 3.179 |
| 土壤水 | 1.0 | 2.707 | 2.757 |

## 🌡️ 参数敏感性分析

### 温度影响 (显著)
- 温度从0°C到40°C，斜率变化约30%
- 温度升高，斜率降低

### 水体类型影响 (重要)
- 开放水体 vs 土壤水，斜率差异约35%
- n值越小(开放程度越高)，斜率越大

### 湿度影响 (相对较小)
- 在当前计算模型中影响不明显
- 需要进一步研究

## 🌍 实际应用场景

| 应用场景 | 环境条件 | Gibson斜率 | Benettin斜率 |
|---------|---------|-----------|-------------|
| 干旱区湖泊蒸发 | 低湿度、高温、开放水体 | 3.496 | 3.553 |
| 湿润区河流蒸发 | 高湿度、低温、开放水体 | 3.814 | 3.874 |
| 农田土壤蒸发 | 中等湿度、中等温度、土壤水 | 2.616 | 2.665 |
| 森林土壤蒸发 | 较高湿度、较低温度、部分覆盖 | 2.954 | 3.007 |

## 📈 数据处理流程

### 1. 数据准备
- **气候数据**: ERA5重分析数据 (温度、相对湿度)
- **同位素数据**: 降水同位素观测数据 (δ²H, δ¹⁸O)
- **空间数据**: 研究区域的点位坐标

### 2. 空间插值
- 使用反距离权重法(IDW)进行空间插值
- 为每个目标点位获取最近的4个观测点
- 计算距离权重并进行插值

### 3. 斜率计算
- 对每个点位的每个月份计算蒸发线斜率
- 支持Gibson和Benettin两种计算方法
- 考虑不同的水体类型(n值)

### 4. 结果输出
- 插值后的气候和同位素数据
- 各点位各月份的蒸发线斜率
- 统计分析结果

## 💾 数据获取指南

### ERA5数据获取
1. **注册账户**: https://cds.climate.copernicus.eu/
2. **安装API**: `pip install cdsapi`
3. **配置密钥**: 创建 `.cdsapirc` 文件
4. **下载数据**: 使用提供的脚本模板
5. **数据处理**: 转换为月平均值格式

### 替代数据源
- 中国气象数据网: http://data.cma.cn/
- NCEP/NCAR重分析数据
- 各地气象局历史数据

## 🎯 项目特点

✅ **科学严谨**: 基于最新科学研究成果  
✅ **方法完整**: 支持多种计算方法比较  
✅ **分析全面**: 完整的参数敏感性分析  
✅ **应用广泛**: 实际应用场景覆盖  
✅ **可视化**: 分析结果图表化展示  
✅ **模块化**: 设计清晰，易于扩展  
✅ **文档完善**: 详细的使用说明和示例  

## 📞 联系方式

- **Email**: <EMAIL>
- **Email**: <EMAIL>

## 🔗 相关文献

1. Horita, J., Wesolowski, D.J., 1994. Liquid-vapor fractionation of oxygen and hydrogen isotopes of water from the freezing to the critical temperature. Geochimica et Cosmochimica Acta 58, 3425–3437.

2. Gibson, J.J., Birks, S.J., Edwards, T.W.D., 2008. Global prediction of δA and δ2H-δ18O evaporation slopes for lakes and soil water accounting for seasonality. Global Biogeochemical Cycles 22.

3. Benettin, P., Volkmann, T.H.M., von Freyberg, J., et al., 2018. Effects of climatic seasonality on the isotopic composition of evaporating soil waters. Hydrology and Earth System Sciences 22, 2881–2890.

4. Ren X., Li P., He X., Zhang Q., 2024. Tracing the sources and evaporation fate of surface water and groundwater using stable isotopes of hydrogen and oxygen. Science of the Total Environment, 172708.

---

**项目完成时间**: 2025年8月2日  
**运行环境**: Python 3.x + pandas + numpy + matplotlib  
**数据处理时间**: 约1.6秒 (10个点位，12个月)  
**总文件数**: 18个文件  
**代码行数**: 约2000行
