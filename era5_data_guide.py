# -*- coding: utf-8 -*-
# ERA5数据获取和处理指南

"""
ERA5数据获取和处理指南
===================

本脚本提供ERA5数据获取和处理的完整指南，用于生成meanEAR5.xlsx文件
"""

def era5_download_guide():
    """ERA5数据下载指南"""
    print("=" * 80)
    print("ERA5数据获取指南")
    print("=" * 80)
    print()
    
    print("🌐 1. 数据源")
    print("-" * 50)
    print("官方网站: https://cds.climate.copernicus.eu/")
    print("数据集名称: ERA5 hourly data on single levels from 1940 to present")
    print("数据集ID: reanalysis-era5-single-levels")
    print()
    
    print("📋 2. 注册步骤")
    print("-" * 50)
    print("1. 访问 https://cds.climate.copernicus.eu/")
    print("2. 点击 'Register' 创建免费账户")
    print("3. 验证邮箱并登录")
    print("4. 接受使用条款")
    print("5. 获取API密钥 (用于程序化下载)")
    print()
    
    print("🔧 3. 安装CDS API")
    print("-" * 50)
    print("pip install cdsapi")
    print()
    
    print("⚙️ 4. 配置API密钥")
    print("-" * 50)
    print("创建文件: ~/.cdsapirc (Linux/Mac) 或 %USERPROFILE%\\.cdsapirc (Windows)")
    print("内容格式:")
    print("url: https://cds.climate.copernicus.eu/api/v2")
    print("key: YOUR_API_KEY")
    print()

def era5_download_script():
    """ERA5数据下载脚本示例"""
    print("💻 5. 数据下载脚本")
    print("-" * 50)
    
    script_content = '''
import cdsapi
import pandas as pd
import xarray as xr

def download_era5_data(year_start, year_end, area, output_file):
    """
    下载ERA5数据
    
    Parameters:
    year_start, year_end: 起始和结束年份
    area: [north, west, south, east] 研究区域边界
    output_file: 输出文件名
    """
    
    c = cdsapi.Client()
    
    # 下载参数
    request = {
        'product_type': 'reanalysis',
        'format': 'netcdf',
        'variable': [
            '2m_temperature',           # 2米气温
            'relative_humidity',        # 相对湿度 (如果可用)
            '2m_dewpoint_temperature',  # 2米露点温度 (用于计算相对湿度)
        ],
        'year': [str(year) for year in range(year_start, year_end + 1)],
        'month': [f'{i:02d}' for i in range(1, 13)],
        'day': [f'{i:02d}' for i in range(1, 32)],
        'time': [f'{i:02d}:00' for i in range(0, 24)],
        'area': area,  # [north, west, south, east]
        'grid': [0.25, 0.25],  # 0.25度分辨率
    }
    
    c.retrieve('reanalysis-era5-single-levels', request, output_file)
    print(f"数据已下载到: {output_file}")

# 示例：下载关中地区数据
# area = [35.0, 107.0, 34.0, 110.0]  # [北, 西, 南, 东]
# download_era5_data(2010, 2020, area, 'era5_guanzhong.nc')
'''
    
    print(script_content)
    print()

def era5_processing_script():
    """ERA5数据处理脚本"""
    print("🔄 6. 数据处理脚本")
    print("-" * 50)
    
    processing_script = '''
import xarray as xr
import pandas as pd
import numpy as np

def calculate_relative_humidity(temp_2m, dewpoint_2m):
    """
    根据温度和露点温度计算相对湿度
    """
    # 使用Magnus公式计算饱和水汽压
    def saturation_vapor_pressure(temp_celsius):
        return 6.112 * np.exp(17.67 * temp_celsius / (temp_celsius + 243.5))
    
    temp_celsius = temp_2m - 273.15  # 开尔文转摄氏度
    dewpoint_celsius = dewpoint_2m - 273.15
    
    es = saturation_vapor_pressure(temp_celsius)  # 饱和水汽压
    e = saturation_vapor_pressure(dewpoint_celsius)  # 实际水汽压
    
    rh = (e / es) * 100  # 相对湿度 (%)
    return rh / 100  # 转换为小数形式

def process_era5_to_monthly_means(input_file, output_file):
    """
    处理ERA5数据为月平均值
    """
    # 读取NetCDF文件
    ds = xr.open_dataset(input_file)
    
    # 提取变量
    temp_2m = ds['t2m']  # 2米气温
    dewpoint_2m = ds['d2m']  # 2米露点温度
    
    # 计算相对湿度
    rh = calculate_relative_humidity(temp_2m, dewpoint_2m)
    
    # 添加相对湿度到数据集
    ds['rh'] = (('time', 'latitude', 'longitude'), rh)
    
    # 计算月平均值
    monthly_means = ds.groupby('time.month').mean('time')
    
    # 转换为DataFrame格式
    data_list = []
    
    for month in range(1, 13):
        month_data = monthly_means.sel(month=month)
        
        for lat in month_data.latitude.values:
            for lon in month_data.longitude.values:
                temp = float(month_data['t2m'].sel(latitude=lat, longitude=lon).values) - 273.15  # 转为摄氏度
                humidity = float(month_data['rh'].sel(latitude=lat, longitude=lon).values)
                
                data_list.append({
                    'lat': lat,
                    'lon': lon, 
                    'month': month,
                    't2m': temp,
                    'rh': humidity
                })
    
    # 创建DataFrame并保存
    df = pd.DataFrame(data_list)
    df.to_excel(output_file, index=False)
    print(f"处理完成，结果保存到: {output_file}")
    
    return df

# 使用示例
# df = process_era5_to_monthly_means('era5_guanzhong.nc', 'meanEAR5.xlsx')
'''
    
    print(processing_script)
    print()

def alternative_data_sources():
    """替代数据源"""
    print("🔄 7. 替代数据源")
    print("-" * 50)
    print("如果无法获取ERA5数据，可以考虑以下替代方案:")
    print()
    print("a) 气象站观测数据:")
    print("   - 中国气象数据网: http://data.cma.cn/")
    print("   - 各地气象局历史数据")
    print()
    print("b) 其他重分析数据:")
    print("   - NCEP/NCAR重分析数据")
    print("   - JRA-55重分析数据")
    print()
    print("c) 卫星遥感数据:")
    print("   - MODIS温度产品")
    print("   - 其他卫星气象产品")
    print()

def create_sample_data():
    """创建示例数据文件"""
    print("📝 8. 创建示例数据")
    print("-" * 50)
    print("如果暂时无法获取真实数据，可以创建示例数据进行测试:")
    print()
    
    # 创建示例数据
    import pandas as pd
    import numpy as np
    
    # 设置随机种子确保结果可重现
    np.random.seed(42)
    
    # 定义研究区域 (关中地区示例)
    lats = np.arange(34.0, 35.1, 0.1)
    lons = np.arange(107.0, 110.1, 0.1)
    
    data = []
    for lat in lats:
        for lon in lons:
            for month in range(1, 13):
                # 模拟温度 (考虑季节变化)
                base_temp = 15 + 10 * np.sin((month - 1) * np.pi / 6)
                temp = base_temp + np.random.normal(0, 2)
                
                # 模拟相对湿度
                base_rh = 0.65 + 0.15 * np.sin((month - 1) * np.pi / 6 + np.pi/4)
                rh = max(0.3, min(0.9, base_rh + np.random.normal(0, 0.05)))
                
                data.append({
                    'lat': lat,
                    'lon': lon,
                    'month': month,
                    't2m': temp,
                    'rh': rh
                })
    
    df = pd.DataFrame(data)
    df.to_excel('meanEAR5_sample.xlsx', index=False)
    
    print("✅ 示例数据已创建: meanEAR5_sample.xlsx")
    print(f"   数据点数: {len(df)}")
    print(f"   纬度范围: {df['lat'].min():.1f} - {df['lat'].max():.1f}")
    print(f"   经度范围: {df['lon'].min():.1f} - {df['lon'].max():.1f}")
    print(f"   温度范围: {df['t2m'].min():.1f} - {df['t2m'].max():.1f} °C")
    print(f"   湿度范围: {df['rh'].min():.2f} - {df['rh'].max():.2f}")
    print()

def main():
    """主函数"""
    era5_download_guide()
    era5_download_script()
    era5_processing_script()
    alternative_data_sources()
    create_sample_data()
    
    print("📋 总结")
    print("-" * 50)
    print("1. 注册CDS账户并获取API密钥")
    print("2. 安装cdsapi包")
    print("3. 下载ERA5原始数据")
    print("4. 处理为月平均值格式")
    print("5. 保存为Excel文件")
    print()
    print("💡 提示: 如果是学习目的，可以先使用示例数据进行测试")
    print("=" * 80)

if __name__ == "__main__":
    main()
