# -*- coding: utf-8 -*-
# 蒸发线斜率计算项目总结

import pandas as pd
import numpy as np
from EL_slope import *

def project_overview():
    """项目概述"""
    print("=" * 80)
    print("蒸发线斜率计算项目 - 完整功能演示")
    print("=" * 80)
    print()
    
    print("📚 项目背景:")
    print("   基于 Ren X. et al. (2024) 在 Science of the Total Environment 发表的研究")
    print("   用于追踪地表水和地下水的来源及蒸发过程")
    print()
    
    print("🔬 科学原理:")
    print("   利用氢氧稳定同位素(δ2H, δ18O)分析水体蒸发过程")
    print("   通过蒸发线斜率定量化蒸发强度和环境条件影响")
    print()
    
    print("📁 项目文件结构:")
    print("   ├── EL_slope.py              # 核心计算模块")
    print("   ├── data_processing.py       # QGIS数据预处理模块")
    print("   ├── test_EL_slope.py         # 基础功能测试")
    print("   ├── analysis_EL_slope.py     # 参数敏感性分析")
    print("   ├── data_processing_demo.py  # 数据处理演示")
    print("   └── demo_complete.py         # 完整功能演示")
    print()

def core_functions_demo():
    """核心函数演示"""
    print("🧮 核心计算函数:")
    print("-" * 50)
    
    # 示例参数
    temperature = 20  # °C
    precipitation = [-38, -6]  # δ2H, δ18O (‰)
    humidity = 0.75
    n = 0.75
    
    print(f"输入参数: T={temperature}°C, RH={humidity}, δ2H={precipitation[0]}‰, δ18O={precipitation[1]}‰")
    print()
    
    # 1. 温度转换
    T_K = Tk(temperature)
    print(f"1. 温度转换: Tk({temperature}) = {T_K} K")
    
    # 2. 平衡分馏系数
    alpha_H = alpha_plus('H', T_K)
    alpha_O = alpha_plus('O', T_K)
    print(f"2. 平衡分馏系数: α+(H)={alpha_H:.6f}, α+(O)={alpha_O:.6f}")
    
    # 3. 平衡分馏因子
    epsilon_H = epsilon_plus(alpha_H)
    epsilon_O = epsilon_plus(alpha_O)
    print(f"3. 平衡分馏因子: ε+(H)={epsilon_H:.3f}‰, ε+(O)={epsilon_O:.3f}‰")
    
    # 4. 动力学分馏因子
    epsilon_k_H_gibson = epsilon_k('H', n, 0, humidity)
    epsilon_k_O_gibson = epsilon_k('O', n, 0, humidity)
    epsilon_k_H_benettin = epsilon_k('H', n, 1, humidity)
    epsilon_k_O_benettin = epsilon_k('O', n, 1, humidity)
    
    print(f"4. 动力学分馏因子:")
    print(f"   Gibson方法:   εk(H)={epsilon_k_H_gibson:.3f}‰, εk(O)={epsilon_k_O_gibson:.3f}‰")
    print(f"   Benettin方法: εk(H)={epsilon_k_H_benettin:.3f}‰, εk(O)={epsilon_k_O_benettin:.3f}‰")
    
    # 5. 大气水汽同位素组成
    delta_A_H = delta_A(precipitation[0], epsilon_H, alpha_H)
    delta_A_O = delta_A(precipitation[1], epsilon_O, alpha_O)
    print(f"5. 大气水汽同位素: δA(H)={delta_A_H:.3f}‰, δA(O)={delta_A_O:.3f}‰")
    
    # 6. 蒸发水同位素组成
    dX_H_gibson = dX('H', precipitation, humidity, temperature, n, 0)
    dX_O_gibson = dX('O', precipitation, humidity, temperature, n, 0)
    dX_H_benettin = dX('H', precipitation, humidity, temperature, n, 1)
    dX_O_benettin = dX('O', precipitation, humidity, temperature, n, 1)
    
    print(f"6. 蒸发水同位素组成:")
    print(f"   Gibson方法:   dX(H)={dX_H_gibson:.3f}‰, dX(O)={dX_O_gibson:.3f}‰")
    print(f"   Benettin方法: dX(H)={dX_H_benettin:.3f}‰, dX(O)={dX_O_benettin:.3f}‰")
    
    # 7. 蒸发线斜率
    slope_gibson = slope_line(precipitation, humidity, temperature, n, 0)
    slope_benettin = slope_line(precipitation, humidity, temperature, n, 1)
    
    print(f"7. 蒸发线斜率:")
    print(f"   Gibson方法:   {slope_gibson:.3f}")
    print(f"   Benettin方法: {slope_benettin:.3f}")
    print()

def applications_demo():
    """应用场景演示"""
    print("🌍 实际应用场景:")
    print("-" * 50)
    
    scenarios = [
        {
            "name": "干旱区湖泊蒸发",
            "params": ([-45, -7], 0.4, 25, 0.5),
            "description": "低湿度、高温、开放水体"
        },
        {
            "name": "湿润区河流蒸发", 
            "params": ([-30, -5], 0.8, 15, 0.5),
            "description": "高湿度、低温、开放水体"
        },
        {
            "name": "农田土壤蒸发",
            "params": ([-35, -6], 0.7, 20, 1.0),
            "description": "中等湿度、中等温度、土壤水"
        },
        {
            "name": "森林土壤蒸发",
            "params": ([-40, -6.5], 0.75, 18, 0.8),
            "description": "较高湿度、较低温度、部分覆盖"
        }
    ]
    
    print("场景\t\t\tGibson斜率\tBenettin斜率\t描述")
    print("-" * 80)
    
    for scenario in scenarios:
        precip, humid, temp, n_val = scenario["params"]
        slope_g = slope_line(precip, humid, temp, n_val, 0)
        slope_b = slope_line(precip, humid, temp, n_val, 1)
        
        print(f"{scenario['name']:15s}\t{slope_g:.3f}\t\t{slope_b:.3f}\t\t{scenario['description']}")
    print()

def parameter_effects_summary():
    """参数影响总结"""
    print("📊 参数影响分析总结:")
    print("-" * 50)
    
    base_params = ([-38, -6], 0.75, 20, 0.75, 1)  # Benettin方法
    base_slope = slope_line(*base_params)
    
    print(f"基准条件斜率: {base_slope:.3f}")
    print()
    
    # 温度影响
    print("🌡️  温度影响 (其他参数不变):")
    temps = [0, 10, 20, 30, 40]
    for t in temps:
        params = ([-38, -6], 0.75, t, 0.75, 1)
        slope = slope_line(*params)
        change = ((slope - base_slope) / base_slope) * 100
        print(f"   {t:2d}°C: 斜率 {slope:.3f} (变化 {change:+5.1f}%)")
    print()
    
    # 湿度影响
    print("💧 湿度影响 (其他参数不变):")
    humidities = [0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
    for h in humidities:
        params = ([-38, -6], h, 20, 0.75, 1)
        slope = slope_line(*params)
        change = ((slope - base_slope) / base_slope) * 100
        print(f"   {h:.1f}: 斜率 {slope:.3f} (变化 {change:+5.1f}%)")
    print()
    
    # n值影响
    print("🏞️  水体类型影响 (n值):")
    n_values = [(0.5, "开放水体"), (0.75, "部分覆盖"), (1.0, "土壤水")]
    for n_val, desc in n_values:
        params = ([-38, -6], 0.75, 20, n_val, 1)
        slope = slope_line(*params)
        change = ((slope - base_slope) / base_slope) * 100
        print(f"   n={n_val} ({desc:8s}): 斜率 {slope:.3f} (变化 {change:+5.1f}%)")
    print()

def data_processing_summary():
    """数据处理流程总结"""
    print("🔄 数据处理流程:")
    print("-" * 50)
    
    print("1. 数据准备阶段:")
    print("   • 气候数据: ERA5重分析数据 (温度、相对湿度)")
    print("   • 同位素数据: 降水同位素观测数据 (δ2H, δ18O)")
    print("   • 空间数据: 研究区域的点位坐标")
    print()
    
    print("2. 空间插值阶段:")
    print("   • 使用反距离权重法(IDW)进行空间插值")
    print("   • 为每个目标点位获取最近的4个观测点")
    print("   • 计算距离权重并进行插值")
    print()
    
    print("3. 斜率计算阶段:")
    print("   • 对每个点位的每个月份计算蒸发线斜率")
    print("   • 支持Gibson和Benettin两种计算方法")
    print("   • 考虑不同的水体类型(n值)")
    print()
    
    print("4. 结果输出:")
    print("   • 插值后的气候和同位素数据")
    print("   • 各点位各月份的蒸发线斜率")
    print("   • 统计分析结果")
    print()

def file_outputs_summary():
    """输出文件总结"""
    print("📄 生成的文件:")
    print("-" * 50)
    
    files = [
        ("interpolated_data_demo.csv", "插值后的气候和同位素数据"),
        ("slope_results_demo.csv", "蒸发线斜率计算结果"),
        ("humidity_effect.png", "湿度对斜率影响的可视化"),
        ("temperature_effect.png", "温度对斜率影响的可视化"),
        ("n_effect.png", "n值对斜率影响的可视化")
    ]
    
    for filename, description in files:
        print(f"   • {filename:25s} - {description}")
    print()

def main():
    """主函数"""
    project_overview()
    core_functions_demo()
    applications_demo()
    parameter_effects_summary()
    data_processing_summary()
    file_outputs_summary()
    
    print("🎯 项目特点:")
    print("-" * 50)
    print("   ✅ 基于最新科学研究成果")
    print("   ✅ 支持多种计算方法比较")
    print("   ✅ 完整的参数敏感性分析")
    print("   ✅ 实际应用场景覆盖")
    print("   ✅ 可视化分析结果")
    print("   ✅ 模块化设计，易于扩展")
    print()
    
    print("📞 联系方式:")
    print("   Email: <EMAIL>")
    print("   Email: <EMAIL>")
    print()
    
    print("=" * 80)
    print("项目演示完成！")
    print("=" * 80)

if __name__ == "__main__":
    main()
