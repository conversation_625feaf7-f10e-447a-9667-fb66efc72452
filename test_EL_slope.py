# -*- coding: utf-8 -*-
# Test script for EL_slope.py

from EL_slope import *
import numpy as np

print("=== 蒸发线斜率计算测试 ===\n")

# 测试参数
temperature = 20  # 摄氏度
humidity = 0.75   # 相对湿度
precipitation = [-38, -6]  # 降水同位素组成 [δ2H, δ18O]
n = 0.75  # 0.5~1, 0.5 for open water bodies; 1 for soil water
method = 1  # 0: <PERSON> et al.(2008), 1: <PERSON><PERSON><PERSON> et al.(2018)

print(f"测试参数:")
print(f"温度: {temperature}°C")
print(f"相对湿度: {humidity}")
print(f"降水同位素组成: δ2H = {precipitation[0]}‰, δ18O = {precipitation[1]}‰")
print(f"n值: {n}")
print(f"方法: {method} ({'<PERSON><PERSON><PERSON> et al.(2018)' if method == 1 else '<PERSON> et al.(2008)'})")
print()

# 1. 温度转换测试
T_kelvin = Tk(temperature)
print(f"1. 温度转换:")
print(f"   {temperature}°C = {T_kelvin} K")
print()

# 2. 计算α+（液-气平衡同位素分馏系数）
alpha_H = alpha_plus('H', T_kelvin)
alpha_O = alpha_plus('O', T_kelvin)
print(f"2. 液-气平衡同位素分馏系数 α+:")
print(f"   氢同位素: α+ = {alpha_H:.6f}")
print(f"   氧同位素: α+ = {alpha_O:.6f}")
print()

# 3. 计算ε+
epsilon_H = epsilon_plus(alpha_H)
epsilon_O = epsilon_plus(alpha_O)
print(f"3. 平衡分馏因子 ε+:")
print(f"   氢同位素: ε+ = {epsilon_H:.3f}‰")
print(f"   氧同位素: ε+ = {epsilon_O:.3f}‰")
print()

# 4. 计算εk（动力学分馏因子）
epsilon_k_H = epsilon_k('H', n, method, humidity)
epsilon_k_O = epsilon_k('O', n, method, humidity)
print(f"4. 动力学分馏因子 εk:")
print(f"   氢同位素: εk = {epsilon_k_H:.3f}‰")
print(f"   氧同位素: εk = {epsilon_k_O:.3f}‰")
print()

# 5. 计算δA
delta_A_H = delta_A(precipitation[0], epsilon_H, alpha_H)
delta_A_O = delta_A(precipitation[1], epsilon_O, alpha_O)
print(f"5. 大气水汽同位素组成 δA:")
print(f"   氢同位素: δA = {delta_A_H:.3f}‰")
print(f"   氧同位素: δA = {delta_A_O:.3f}‰")
print()

# 6. 计算dX（蒸发水的同位素组成）
dX_H = dX('H', precipitation, humidity, temperature, n, method)
dX_O = dX('O', precipitation, humidity, temperature, n, method)
print(f"6. 蒸发水同位素组成 dX:")
print(f"   氢同位素: dX_H = {dX_H:.3f}‰")
print(f"   氧同位素: dX_O = {dX_O:.3f}‰")
print()

# 7. 计算蒸发线斜率
slope = slope_line(precipitation, humidity, temperature, n, method)
print(f"7. 蒸发线斜率:")
print(f"   斜率 = dX_H / dX_O = {slope:.3f}")
print()

# 8. 不同条件下的斜率比较
print("8. 不同条件下的斜率比较:")
print("   湿度\t方法\t斜率")
print("   ----\t----\t----")

humidities = [0.5, 0.65, 0.75, 0.85]
methods = [0, 1]

for h in humidities:
    for m in methods:
        s = slope_line(precipitation, h, temperature, n, m)
        method_name = "Gibson" if m == 0 else "Benettin"
        print(f"   {h}\t{method_name}\t{s:.3f}")

print()
print("=== 测试完成 ===")
