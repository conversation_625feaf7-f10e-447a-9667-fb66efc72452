# -*- coding: utf-8 -*-
# 蒸发线斜率分析脚本

from EL_slope import *
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def analyze_humidity_effect():
    """分析湿度对蒸发线斜率的影响"""
    print("=== 湿度对蒸发线斜率的影响 ===")
    
    # 参数设置
    precipitation = [-38, -6]
    temperature = 20
    n = 0.75
    
    humidities = np.linspace(0.3, 0.9, 13)
    slopes_gibson = []
    slopes_benettin = []
    
    for h in humidities:
        slope_g = slope_line(precipitation, h, temperature, n, 0)  # Gibson方法
        slope_b = slope_line(precipitation, h, temperature, n, 1)  # Benettin方法
        slopes_gibson.append(slope_g)
        slopes_benettin.append(slope_b)
    
    # 打印结果
    print("湿度\tGibson斜率\tBenettin斜率")
    print("----\t---------\t-----------")
    for i, h in enumerate(humidities):
        print(f"{h:.2f}\t{slopes_gibson[i]:.3f}\t\t{slopes_benettin[i]:.3f}")
    
    # 绘图
    plt.figure(figsize=(10, 6))
    plt.plot(humidities, slopes_gibson, 'b-o', label='Gibson et al. (2008)', linewidth=2)
    plt.plot(humidities, slopes_benettin, 'r-s', label='Benettin et al. (2018)', linewidth=2)
    plt.xlabel('相对湿度')
    plt.ylabel('蒸发线斜率')
    plt.title('湿度对蒸发线斜率的影响')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('humidity_effect.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("图表已保存为 humidity_effect.png\n")

def analyze_temperature_effect():
    """分析温度对蒸发线斜率的影响"""
    print("=== 温度对蒸发线斜率的影响 ===")
    
    # 参数设置
    precipitation = [-38, -6]
    humidity = 0.75
    n = 0.75
    
    temperatures = np.linspace(0, 40, 21)
    slopes_gibson = []
    slopes_benettin = []
    
    for t in temperatures:
        slope_g = slope_line(precipitation, humidity, t, n, 0)
        slope_b = slope_line(precipitation, humidity, t, n, 1)
        slopes_gibson.append(slope_g)
        slopes_benettin.append(slope_b)
    
    # 打印结果
    print("温度(°C)\tGibson斜率\tBenettin斜率")
    print("-------\t---------\t-----------")
    for i, t in enumerate(temperatures):
        print(f"{t:.1f}\t\t{slopes_gibson[i]:.3f}\t\t{slopes_benettin[i]:.3f}")
    
    # 绘图
    plt.figure(figsize=(10, 6))
    plt.plot(temperatures, slopes_gibson, 'b-o', label='Gibson et al. (2008)', linewidth=2)
    plt.plot(temperatures, slopes_benettin, 'r-s', label='Benettin et al. (2018)', linewidth=2)
    plt.xlabel('温度 (°C)')
    plt.ylabel('蒸发线斜率')
    plt.title('温度对蒸发线斜率的影响')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('temperature_effect.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("图表已保存为 temperature_effect.png\n")

def analyze_n_effect():
    """分析n值对蒸发线斜率的影响"""
    print("=== n值对蒸发线斜率的影响 ===")
    
    # 参数设置
    precipitation = [-38, -6]
    humidity = 0.75
    temperature = 20
    
    n_values = np.linspace(0.5, 1.0, 11)
    slopes_gibson = []
    slopes_benettin = []
    
    for n in n_values:
        slope_g = slope_line(precipitation, humidity, temperature, n, 0)
        slope_b = slope_line(precipitation, humidity, temperature, n, 1)
        slopes_gibson.append(slope_g)
        slopes_benettin.append(slope_b)
    
    # 打印结果
    print("n值\tGibson斜率\tBenettin斜率")
    print("---\t---------\t-----------")
    for i, n in enumerate(n_values):
        print(f"{n:.2f}\t{slopes_gibson[i]:.3f}\t\t{slopes_benettin[i]:.3f}")
    
    # 绘图
    plt.figure(figsize=(10, 6))
    plt.plot(n_values, slopes_gibson, 'b-o', label='Gibson et al. (2008)', linewidth=2)
    plt.plot(n_values, slopes_benettin, 'r-s', label='Benettin et al. (2018)', linewidth=2)
    plt.xlabel('n值')
    plt.ylabel('蒸发线斜率')
    plt.title('n值对蒸发线斜率的影响\n(n=0.5: 开放水体; n=1.0: 土壤水)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('n_effect.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("图表已保存为 n_effect.png\n")

if __name__ == "__main__":
    print("蒸发线斜率分析程序")
    print("=" * 50)
    
    try:
        analyze_humidity_effect()
        analyze_temperature_effect() 
        analyze_n_effect()
        
        print("所有分析完成！")
        print("生成的图表文件:")
        print("- humidity_effect.png")
        print("- temperature_effect.png") 
        print("- n_effect.png")
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装matplotlib: pip install matplotlib")
    except Exception as e:
        print(f"运行错误: {e}")
