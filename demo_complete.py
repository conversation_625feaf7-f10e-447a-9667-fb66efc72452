# -*- coding: utf-8 -*-
# 蒸发线斜率计算项目完整演示

from EL_slope import *
import numpy as np
import pandas as pd

def demo_basic_calculations():
    """基础计算演示"""
    print("=" * 60)
    print("蒸发线斜率计算项目演示")
    print("=" * 60)
    print()
    
    print("1. 项目简介")
    print("-" * 30)
    print("本项目用于计算蒸发线斜率，基于以下研究:")
    print("Ren X., Li P., He <PERSON>., Zhang Q., 2024.")
    print("Tracing the sources and evaporation fate of surface water")
    print("and groundwater using stable isotopes of hydrogen and oxygen.")
    print("Science of the Total Environment, 172708.")
    print()
    
    print("2. 主要功能模块")
    print("-" * 30)
    print("- EL_slope.py: 核心计算模块")
    print("- data_processing.py: 数据预处理模块(需要QGIS环境)")
    print("- 支持两种计算方法:")
    print("  * <PERSON> et al. (2008)")
    print("  * <PERSON><PERSON><PERSON> et al. (2018)")
    print()

def demo_single_calculation():
    """单点计算演示"""
    print("3. 单点计算演示")
    print("-" * 30)
    
    # 示例参数
    precipitation = [-38, -6]  # δ2H, δ18O (‰)
    humidity = 0.75           # 相对湿度
    temperature = 20          # 温度 (°C)
    n = 0.75                 # 0.5(开放水体) ~ 1.0(土壤水)
    method = 1               # 0: Gibson, 1: Benettin
    
    print(f"输入参数:")
    print(f"  降水同位素组成: δ2H = {precipitation[0]}‰, δ18O = {precipitation[1]}‰")
    print(f"  相对湿度: {humidity}")
    print(f"  温度: {temperature}°C")
    print(f"  n值: {n}")
    print(f"  计算方法: {'Benettin et al.(2018)' if method == 1 else 'Gibson et al.(2008)'}")
    print()
    
    # 计算过程
    T_K = Tk(temperature)
    alpha_H = alpha_plus('H', T_K)
    alpha_O = alpha_plus('O', T_K)
    
    epsilon_H = epsilon_plus(alpha_H)
    epsilon_O = epsilon_plus(alpha_O)
    
    epsilon_k_H = epsilon_k('H', n, method, humidity)
    epsilon_k_O = epsilon_k('O', n, method, humidity)
    
    dX_H = dX('H', precipitation, humidity, temperature, n, method)
    dX_O = dX('O', precipitation, humidity, temperature, n, method)
    
    slope = slope_line(precipitation, humidity, temperature, n, method)
    
    print("计算结果:")
    print(f"  液-气平衡分馏系数: α+(H) = {alpha_H:.6f}, α+(O) = {alpha_O:.6f}")
    print(f"  平衡分馏因子: ε+(H) = {epsilon_H:.3f}‰, ε+(O) = {epsilon_O:.3f}‰")
    print(f"  动力学分馏因子: εk(H) = {epsilon_k_H:.3f}‰, εk(O) = {epsilon_k_O:.3f}‰")
    print(f"  蒸发水同位素组成: dX(H) = {dX_H:.3f}‰, dX(O) = {dX_O:.3f}‰")
    print(f"  蒸发线斜率: {slope:.3f}")
    print()

def demo_parameter_sensitivity():
    """参数敏感性分析演示"""
    print("4. 参数敏感性分析")
    print("-" * 30)
    
    # 基准参数
    precipitation = [-38, -6]
    humidity = 0.75
    temperature = 20
    n = 0.75
    method = 1

    print(f"基准参数: 降水{precipitation}, 湿度{humidity}, 温度{temperature}°C, n={n}, 方法{method}")
    base_slope = slope_line(precipitation, humidity, temperature, n, method)
    print(f"基准斜率: {base_slope:.3f}")
    print()

    # 湿度敏感性
    print("湿度敏感性分析:")
    humidities = [0.5, 0.6, 0.7, 0.8, 0.9]
    for h in humidities:
        s = slope_line(precipitation, h, temperature, n, method)
        change = ((s - base_slope) / base_slope) * 100
        print(f"  湿度 {h}: 斜率 {s:.3f} (变化 {change:+.1f}%)")
    print()

    # 温度敏感性
    print("温度敏感性分析:")
    temperatures = [0, 10, 20, 30, 40]
    for t in temperatures:
        s = slope_line(precipitation, humidity, t, n, method)
        change = ((s - base_slope) / base_slope) * 100
        print(f"  温度 {t}°C: 斜率 {s:.3f} (变化 {change:+.1f}%)")
    print()

    # n值敏感性
    print("n值敏感性分析:")
    n_values = [0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
    for n_val in n_values:
        s = slope_line(precipitation, humidity, temperature, n_val, method)
        change = ((s - base_slope) / base_slope) * 100
        water_type = "开放水体" if n_val == 0.5 else "土壤水" if n_val == 1.0 else "中间状态"
        print(f"  n={n_val} ({water_type}): 斜率 {s:.3f} (变化 {change:+.1f}%)")
    print()

def demo_method_comparison():
    """方法比较演示"""
    print("5. 计算方法比较")
    print("-" * 30)
    
    test_conditions = [
        ([-38, -6], 0.75, 20, 0.75),
        ([-50, -8], 0.65, 15, 0.5),
        ([-25, -4], 0.85, 25, 1.0),
    ]

    print("条件\t\t\tGibson方法\tBenettin方法\t差异")
    print("-" * 70)

    for i, (precip, humid, temp, n_val) in enumerate(test_conditions, 1):
        slope_gibson = slope_line(precip, humid, temp, n_val, 0)
        slope_benettin = slope_line(precip, humid, temp, n_val, 1)
        diff = slope_benettin - slope_gibson

        print(f"条件{i}\t\t\t{slope_gibson:.3f}\t\t{slope_benettin:.3f}\t\t{diff:+.3f}")
    print()

def demo_practical_applications():
    """实际应用示例"""
    print("6. 实际应用示例")
    print("-" * 30)
    
    print("应用场景:")
    print("- 地表水蒸发过程研究")
    print("- 地下水补给来源追踪")
    print("- 土壤水分蒸发分析")
    print("- 水循环过程定量化")
    print()
    
    print("典型环境条件下的蒸发线斜率:")
    
    scenarios = [
        ("干旱区湖泊", [-45, -7], 0.4, 25, 0.5),
        ("湿润区河流", [-30, -5], 0.8, 15, 0.5),
        ("农田土壤", [-35, -6], 0.7, 20, 1.0),
        ("森林土壤", [-40, -6.5], 0.75, 18, 0.8),
    ]

    for name, precip, humid, temp, n_val in scenarios:
        slope_g = slope_line(precip, humid, temp, n_val, 0)
        slope_b = slope_line(precip, humid, temp, n_val, 1)
        print(f"{name:10s}: Gibson={slope_g:.3f}, Benettin={slope_b:.3f}")
    print()

if __name__ == "__main__":
    demo_basic_calculations()
    demo_single_calculation()
    demo_parameter_sensitivity()
    demo_method_comparison()
    demo_practical_applications()
    
    print("=" * 60)
    print("演示完成！")
    print()
    print("相关文件:")
    print("- EL_slope.py: 核心计算函数")
    print("- test_EL_slope.py: 基础测试脚本")
    print("- analysis_EL_slope.py: 参数影响分析脚本")
    print("- data_processing.py: QGIS数据处理脚本")
    print()
    print("生成的图表文件:")
    print("- humidity_effect.png: 湿度影响分析")
    print("- temperature_effect.png: 温度影响分析")
    print("- n_effect.png: n值影响分析")
    print("=" * 60)
